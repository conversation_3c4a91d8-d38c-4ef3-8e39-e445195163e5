import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEnum, IsObject } from 'class-validator';
import { SmsProviderType, SmsProviderConfig } from '../../../interfaces/sms-provider-config.interface';

/**
 * DTO cho việc test kết nối với nhà cung cấp SMS
 */
export class TestSmsConnectionDto {
  @ApiProperty({
    description: 'Tên nhà cung cấp SMS',
    enum: SmsProviderType,
    example: SmsProviderType.FPT_SMS,
  })
  @IsNotEmpty({ message: 'Tên nhà cung cấp không được để trống' })
  @IsEnum(SmsProviderType, { message: 'Tên nhà cung cấp không hợp lệ' })
  providerName: SmsProviderType;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho nhà cung cấp SMS',
    type: 'object',
    additionalProperties: true,
    example: {
      providerName: 'FPT_SMS',
      clientId: 'your-client-id',
      clientSecret: 'your-client-secret',
      brandName: 'YourBrand',
      apiUrl: 'http://api.fpt.net/api',
      scope: 'send_brandname_otp send_brandname'
    },
  })
  @IsNotEmpty({ message: 'Cấu hình chi tiết không được để trống' })
  @IsObject({ message: 'Cấu hình chi tiết phải là đối tượng' })
  config: SmsProviderConfig;
}

/**
 * DTO cho việc test kết nối với cấu hình đã lưu
 */
export class TestSmsConnectionWithConfigDto {
  @ApiProperty({
    description: 'ID của cấu hình SMS đã lưu',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID cấu hình không được để trống' })
  configId: number;
}
