# Cập Nhật API Lấy Chi Tiết Audience - Thêm ConfigJson

## Tổng Quan
Đã cập nhật API lấy chi tiết audience để join với bảng `audience_user_custom_fields` (definition table) và trả về thêm trường `configJson` trong metadata của custom fields.

## Các Thay Đổi Đã Thực Hiện

### 1. Cập Nhật CustomFieldResponseDto
**File**: `src/modules/marketing/user/dto/audience/custom-field-response.dto.ts`

**Thay đổi**:
- Thêm trường `configJson?: any` vào DTO
- Thêm documentation và example cho trường mới

**Cấu trúc mới**:
```typescript
export class CustomFieldResponseDto {
  id: number;
  audienceId: number;
  fieldId: number;
  fieldValue: any;
  configJson?: any; // ← TRƯỜNG MỚI
  createdAt: number;
  updatedAt: number;
}
```

### 2. Cậ<PERSON> Nhật UserAudienceService
**File**: `src/modules/marketing/user/services/user-audience.service.ts`

**Thay đổi**:
- Thêm import `UserAudienceCustomFieldDefinitionRepository`
- Thêm repository vào constructor
- Thêm method `getCustomFieldsWithDefinitionForMultiple()` - xử lý nhiều audience
- Thêm method `getCustomFieldsWithDefinition()` - xử lý một audience
- Cập nhật `mapToDto()` để map `configJson`
- Cập nhật tất cả methods sử dụng custom fields

**Methods đã cập nhật**:
- `create()` - Sử dụng `getCustomFieldsWithDefinition()`
- `update()` - Sử dụng `getCustomFieldsWithDefinition()`
- `findOne()` - Sử dụng `getCustomFieldsWithDefinition()`
- `findAll()` - Sử dụng `getCustomFieldsWithDefinitionForMultiple()`
- `updateAvatar()` - Sử dụng `getCustomFieldsWithDefinition()`
- `removeAvatar()` - Sử dụng `getCustomFieldsWithDefinition()`

### 3. Logic Join Với Definition Table

**Method mới**: `getCustomFieldsWithDefinitionForMultiple()`
```typescript
private async getCustomFieldsWithDefinitionForMultiple(audienceIds: number[], userId: number): Promise<Record<number, any[]>> {
  // 1. Lấy tất cả custom fields của các audience
  const customFields = await this.userAudienceCustomFieldRepository.find({
    where: { audienceId: In(audienceIds) }
  });

  // 2. Lấy tất cả fieldIds unique
  const fieldIds = [...new Set(customFields.map(cf => cf.fieldId))];
  
  // 3. Lấy definitions từ bảng audience_user_custom_fields
  const definitions = await this.userAudienceCustomFieldDefinitionRepository.find({
    where: { 
      id: In(fieldIds),
      userId 
    }
  });

  // 4. Tạo map definition theo fieldId
  const definitionMap = definitions.reduce((map, def) => {
    map[def.id] = def;
    return map;
  }, {} as Record<number, any>);

  // 5. Group và merge custom fields với definition
  const result: Record<number, any[]> = {};
  for (const field of customFields) {
    if (!result[field.audienceId]) {
      result[field.audienceId] = [];
    }
    
    result[field.audienceId].push({
      ...field,
      configJson: definitionMap[field.fieldId]?.configJson || null
    });
  }

  return result;
}
```

## Cấu Trúc Response Mới

### Trước (chỉ có fieldId và fieldValue):
```json
{
  "customFields": [
    {
      "id": 1,
      "audienceId": 1,
      "fieldId": 5,
      "fieldValue": true,
      "createdAt": 1619171200,
      "updatedAt": 1619171200
    }
  ]
}
```

### Sau (có thêm configJson):
```json
{
  "customFields": [
    {
      "id": 1,
      "audienceId": 1,
      "fieldId": 5,
      "fieldValue": true,
      "configJson": {
        "id": "nam_nu",
        "type": "select",
        "label": "Nam hay Nữ",
        "options": [
          {"label": "Nam", "value": "nam"},
          {"label": "Nữ", "value": "nu"}
        ],
        "validation": {
          "options": [
            {"label": "Nam", "value": "nam"},
            {"label": "Nữ", "value": "nu"}
          ]
        },
        "displayName": "Nam hay Nữ"
      },
      "createdAt": 1619171200,
      "updatedAt": 1619171200
    }
  ]
}
```

## APIs Được Cập Nhật

1. **GET /marketing/audiences/:id** - Lấy chi tiết audience
2. **GET /marketing/audiences** - Lấy danh sách audience với phân trang
3. **POST /marketing/audiences** - Tạo audience mới
4. **PUT /marketing/audiences/:id** - Cập nhật audience
5. **PUT /marketing/audiences/:id/avatar** - Cập nhật avatar
6. **DELETE /marketing/audiences/:id/avatar** - Xóa avatar

## Performance Optimization

- Sử dụng `getCustomFieldsWithDefinitionForMultiple()` cho API danh sách để tránh N+1 query
- Chỉ query definition một lần cho tất cả fieldIds unique
- Sử dụng Map để tra cứu nhanh definition theo fieldId

## Testing

Đã tạo file test: `test-audience-detail-api.http` để test các API đã cập nhật.

## Lưu Ý

- Admin audience service chưa được cập nhật (có thể cập nhật tương tự nếu cần)
- Cần đảm bảo bảng `audience_user_custom_fields` có dữ liệu configJson
- Trường `configJson` có thể null nếu không tìm thấy definition tương ứng
