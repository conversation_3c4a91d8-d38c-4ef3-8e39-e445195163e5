import { ApiProperty } from '@nestjs/swagger';
import { SmsProviderType, SmsProviderConfig } from '../../../interfaces/sms-provider-config.interface';

/**
 * DTO cho response trả về thông tin cấu hình máy chủ SMS
 */
export class SmsServerResponseDto {
  @ApiProperty({
    description: 'ID của cấu hình máy chủ SMS',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng sở hữu cấu hình',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'Tên nhà cung cấp SMS',
    enum: SmsProviderType,
    example: SmsProviderType.FPT_SMS,
  })
  providerName: SmsProviderType;

  @ApiProperty({
    description: 'Khóa API hoặc token của nhà cung cấp (được ẩn một phần vì bảo mật)',
    example: 'your-api-***',
    nullable: true,
  })
  apiKey: string | null;

  @ApiProperty({
    description: 'URL endpoint để gọi API',
    example: 'http://api.fpt.net/api',
    nullable: true,
  })
  endpoint: string | null;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho nhà cung cấp SMS (thông tin nhạy cảm được ẩn)',
    type: 'object',
    additionalProperties: true,
    example: {
      providerName: 'FPT_SMS',
      clientId: 'your-client-***',
      brandName: 'YourBrand',
      apiUrl: 'http://api.fpt.net/api',
      scope: 'send_brandname_otp send_brandname'
    },
  })
  additionalSettings: Partial<SmsProviderConfig>;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1672531200000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1672531200000,
  })
  updatedAt: number;
}

/**
 * DTO cho response test kết nối SMS
 */
export class TestSmsConnectionResponseDto {
  @ApiProperty({
    description: 'Trạng thái kết nối',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông điệp kết quả',
    example: 'Kết nối thành công với FPT SMS',
  })
  message: string;

  @ApiProperty({
    description: 'Chi tiết lỗi (nếu có)',
    example: null,
    nullable: true,
  })
  error?: string;

  @ApiProperty({
    description: 'Thông tin bổ sung từ nhà cung cấp',
    type: 'object',
    additionalProperties: true,
    example: { provider: 'FPT_SMS', responseTime: 150 },
    nullable: true,
  })
  details?: Record<string, any>;
}
