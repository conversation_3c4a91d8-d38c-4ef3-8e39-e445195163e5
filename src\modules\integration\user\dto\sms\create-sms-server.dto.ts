import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SmsProviderType, SmsProviderConfig } from '../../../interfaces/sms-provider-config.interface';

/**
 * DTO cho việc tạo mới cấu hình máy chủ SMS
 */
export class CreateSmsServerDto {
  @ApiProperty({
    description: 'Tên nhà cung cấp SMS',
    enum: SmsProviderType,
    example: SmsProviderType.FPT_SMS,
  })
  @IsNotEmpty({ message: 'Tên nhà cung cấp không được để trống' })
  @IsEnum(SmsProviderType, { message: 'Tên nhà cung cấp không hợp lệ' })
  providerName: SmsProviderType;

  @ApiProperty({
    description: 'Khóa API hoặc token của nhà cung cấp',
    example: 'your-api-key-here',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'API Key phải là chuỗi' })
  apiKey?: string;

  @ApiProperty({
    description: 'URL endpoint để gọi API (có thể NULL nếu nhà cung cấp đã cố định)',
    example: 'http://api.fpt.net/api',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Endpoint phải là chuỗi' })
  endpoint?: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho nhà cung cấp SMS',
    type: 'object',
    additionalProperties: true,
    example: {
      providerName: 'FPT_SMS',
      clientId: 'your-client-id',
      clientSecret: 'your-client-secret',
      brandName: 'YourBrand',
      apiUrl: 'http://api.fpt.net/api',
      scope: 'send_brandname_otp send_brandname'
    },
  })
  @IsNotEmpty({ message: 'Cấu hình chi tiết không được để trống' })
  @IsObject({ message: 'Cấu hình chi tiết phải là đối tượng' })
  additionalSettings: SmsProviderConfig;
}

/**
 * DTO riêng cho cấu hình FPT SMS
 */
export class CreateFptSmsServerDto {
  @ApiProperty({
    description: 'Client ID dùng cho xác thực OAuth',
    example: 'your-client-id',
  })
  @IsNotEmpty({ message: 'Client ID không được để trống' })
  @IsString({ message: 'Client ID phải là chuỗi' })
  clientId: string;

  @ApiProperty({
    description: 'Client Secret dùng cho xác thực OAuth',
    example: 'your-client-secret',
  })
  @IsNotEmpty({ message: 'Client Secret không được để trống' })
  @IsString({ message: 'Client Secret phải là chuỗi' })
  clientSecret: string;

  @ApiProperty({
    description: 'Brandname mặc định',
    example: 'YourBrand',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Brand Name phải là chuỗi' })
  brandName?: string;

  @ApiProperty({
    description: 'URL cơ sở của API FPT SMS',
    example: 'http://api.fpt.net/api',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'API URL phải là chuỗi' })
  apiUrl?: string;

  @ApiProperty({
    description: 'Phạm vi quyền truy cập cho xác thực OAuth',
    example: 'send_brandname_otp send_brandname',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Scope phải là chuỗi' })
  scope?: string;
}
