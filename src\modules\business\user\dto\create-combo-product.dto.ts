import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ProductImageDto, 
  ProductImagesDto, 
  ProductTagDto, 
  ProductTagsDto, 
  CustomFieldDto, 
  ProductMetadataDto 
} from './create-physical-product.dto';

/**
 * DTO cho giá sản phẩm combo
 */
export class ComboProductPriceDto {
  @ApiProperty({
    description: 'Giá niêm yết',
    example: 800000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  listPrice?: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 650000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salePrice?: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

/**
 * DTO cho item trong combo - phiên bản đơn giản cho API tạo mới
 */
export class ComboItemDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  total: number;
}

/**
 * DTO cho thông tin combo
 */
export class ComboInfoDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm trong combo',
    type: [ComboItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComboItemDto)
  info: ComboItemDto[];
}

/**
 * DTO cho việc tạo sản phẩm combo
 */
export class CreateComboProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Combo áo thun + quần jean',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là COMBO',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.COMBO,
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum.COMBO;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: ComboProductPriceDto,
    example: {
      listPrice: 800000,
      salePrice: 650000,
      currency: 'VND'
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ComboProductPriceDto)
  price: ComboProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Combo áo thun nam + quần jean với giá ưu đãi',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  images?: ProductImagesDto;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Thông tin combo sản phẩm',
    type: ComboInfoDto,
    example: {
      info: [
        {
          productId: 123,
          total: 2
        },
        {
          productId: 456,
          total: 1
        }
      ]
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ComboInfoDto)
  combo: ComboInfoDto;

  @ApiProperty({
    description: 'Danh sách hình ảnh cho advanced info',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  advancedImages?: ProductImagesDto;
}
