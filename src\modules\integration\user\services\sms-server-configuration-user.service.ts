import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { SmsServerConfigurationRepository } from '../../repositories';
import { SmsServerConfiguration } from '../../entities';
import {
  CreateSmsServerDto,
  UpdateSmsServerDto,
  SmsServerResponseDto,
  TestSmsConnectionDto,
  TestSmsConnectionWithConfigDto,
  TestSmsConnectionResponseDto
} from '../dto/sms';
import { SmsProviderType, FptSmsProviderConfig } from '../../interfaces/sms-provider-config.interface';
import { FptSmsProvider } from '@shared/services/sms';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SmsServerConfigurationUserService {
  private readonly logger = new Logger(SmsServerConfigurationUserService.name);

  constructor(
    private readonly smsServerConfigurationRepository: SmsServerConfigurationRepository,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo mới cấu hình máy chủ SMS
   */
  async create(createDto: CreateSmsServerDto, userId: number): Promise<SmsServerResponseDto> {
    try {
      this.logger.log(`Tạo cấu hình SMS mới cho user ${userId}, provider: ${createDto.providerName}`);

      // Validate cấu hình theo từng provider
      await this.validateProviderConfig(createDto.providerName, createDto.additionalSettings);

      const smsConfig = new SmsServerConfiguration();
      smsConfig.userId = userId;
      smsConfig.providerName = createDto.providerName;
      smsConfig.apiKey = createDto.apiKey || null;
      smsConfig.endpoint = createDto.endpoint || null;
      smsConfig.additionalSettings = createDto.additionalSettings;
      smsConfig.createdAt = Date.now();
      smsConfig.updatedAt = Date.now();

      const savedConfig = await this.smsServerConfigurationRepository.save(smsConfig);

      return this.toResponseDto(savedConfig);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình máy chủ SMS
   */
  async update(id: number, updateDto: UpdateSmsServerDto, userId: number): Promise<SmsServerResponseDto> {
    try {
      this.logger.log(`Cập nhật cấu hình SMS ${id} cho user ${userId}`);

      const existingConfig = await this.smsServerConfigurationRepository.findOne({
        where: { id, userId }
      });

      if (!existingConfig) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      // Validate cấu hình nếu có thay đổi
      if (updateDto.providerName && updateDto.additionalSettings) {
        await this.validateProviderConfig(updateDto.providerName, updateDto.additionalSettings);
      }

      // Cập nhật các trường
      if (updateDto.providerName) existingConfig.providerName = updateDto.providerName;
      if (updateDto.apiKey !== undefined) existingConfig.apiKey = updateDto.apiKey;
      if (updateDto.endpoint !== undefined) existingConfig.endpoint = updateDto.endpoint;
      if (updateDto.additionalSettings) existingConfig.additionalSettings = updateDto.additionalSettings;
      existingConfig.updatedAt = Date.now();

      const updatedConfig = await this.smsServerConfigurationRepository.save(existingConfig);

      return this.toResponseDto(updatedConfig);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách cấu hình SMS của user
   */
  async findAll(userId: number): Promise<SmsServerResponseDto[]> {
    try {
      const configs = await this.smsServerConfigurationRepository.find({
        where: { userId }
      });

      return configs.map(config => this.toResponseDto(config));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy cấu hình SMS theo ID
   */
  async findOne(id: number, userId: number): Promise<SmsServerResponseDto> {
    try {
      const config = await this.smsServerConfigurationRepository.findOne({
        where: { id, userId }
      });

      if (!config) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      return this.toResponseDto(config);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy cấu hình SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa cấu hình SMS
   */
  async remove(id: number, userId: number): Promise<void> {
    try {
      const config = await this.smsServerConfigurationRepository.findOne({
        where: { id, userId }
      });

      if (!config) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      await this.smsServerConfigurationRepository.remove(config);
      this.logger.log(`Đã xóa cấu hình SMS ${id} của user ${userId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cấu hình SMS: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Test kết nối với cấu hình mới
   */
  async testConnection(testDto: TestSmsConnectionDto): Promise<TestSmsConnectionResponseDto> {
    try {
      this.logger.log(`Test kết nối SMS với provider: ${testDto.providerName}`);

      switch (testDto.providerName) {
        case SmsProviderType.FPT_SMS:
          return await this.testFptSmsConnection(testDto.config as FptSmsProviderConfig);

        default:
          throw new BadRequestException(`Provider ${testDto.providerName} chưa được hỗ trợ`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi test kết nối SMS: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Kết nối thất bại',
        error: error.message
      };
    }
  }

  /**
   * Test kết nối với cấu hình đã lưu
   */
  async testConnectionWithConfig(testDto: TestSmsConnectionWithConfigDto, userId: number): Promise<TestSmsConnectionResponseDto> {
    try {
      const config = await this.smsServerConfigurationRepository.findOne({
        where: { id: testDto.configId, userId }
      });

      if (!config) {
        throw new NotFoundException('Không tìm thấy cấu hình SMS');
      }

      return await this.testConnection({
        providerName: config.providerName as SmsProviderType,
        config: config.additionalSettings
      });
    } catch (error) {
      this.logger.error(`Lỗi khi test kết nối SMS với config đã lưu: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Kết nối thất bại',
        error: error.message
      };
    }
  }

  /**
   * Test kết nối FPT SMS
   */
  private async testFptSmsConnection(config: FptSmsProviderConfig): Promise<TestSmsConnectionResponseDto> {
    try {
      const fptProvider = new FptSmsProvider(this.httpService, this.configService);
      const result = await fptProvider.testConnection(config);

      return {
        success: result.success,
        message: result.message,
        details: { provider: 'FPT_SMS', responseTime: Date.now() }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Kết nối FPT SMS thất bại',
        error: error.message
      };
    }
  }

  /**
   * Validate cấu hình theo provider
   */
  private async validateProviderConfig(providerName: SmsProviderType, config: any): Promise<void> {
    switch (providerName) {
      case SmsProviderType.FPT_SMS:
        this.validateFptSmsConfig(config);
        break;

      default:
        throw new BadRequestException(`Provider ${providerName} chưa được hỗ trợ`);
    }
  }

  /**
   * Validate cấu hình FPT SMS
   */
  private validateFptSmsConfig(config: any): void {
    if (!config.clientId || !config.clientSecret) {
      throw new BadRequestException('FPT SMS yêu cầu clientId và clientSecret');
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   */
  private toResponseDto(config: SmsServerConfiguration): SmsServerResponseDto {
    // Ẩn thông tin nhạy cảm
    const sanitizedSettings = this.sanitizeSettings(config.additionalSettings);

    return {
      id: config.id,
      userId: config.userId,
      providerName: config.providerName as SmsProviderType,
      apiKey: config.apiKey ? this.maskSensitiveData(config.apiKey) : null,
      endpoint: config.endpoint,
      additionalSettings: sanitizedSettings,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt
    };
  }

  /**
   * Ẩn thông tin nhạy cảm trong settings
   */
  private sanitizeSettings(settings: any): any {
    if (!settings) return settings;

    const sanitized = { ...settings };

    // Ẩn các trường nhạy cảm
    if (sanitized.clientSecret) {
      sanitized.clientSecret = this.maskSensitiveData(sanitized.clientSecret);
    }
    if (sanitized.apiKey) {
      sanitized.apiKey = this.maskSensitiveData(sanitized.apiKey);
    }
    if (sanitized.authToken) {
      sanitized.authToken = this.maskSensitiveData(sanitized.authToken);
    }

    return sanitized;
  }

  /**
   * Ẩn dữ liệu nhạy cảm
   */
  private maskSensitiveData(data: string): string {
    if (!data || data.length <= 6) return '***';
    return data.substring(0, 3) + '***' + data.substring(data.length - 3);
  }
}
